<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\School\Assessment\ScheduleController;
use App\Http\Controllers\School\Assessment\AssessmentProcessController;
use App\Http\Controllers\School\Assessment\QuestionController;
use App\Http\Controllers\School\Assessment\PdfZipController;
use App\Http\Controllers\School\Assessment\AssessmentTeacherController;
use App\Http\Controllers\School\Assessment\AssessmentStudentController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// 学校端 测评 相关模块路由
Route::group(['prefix' => 'v1/assessment', 'middleware' => ['auth.refresh']], function () {
    // 教务端 测评相关接口
    Route::prefix('teacher_platform')->group(function () {
        // 教务端 测评所有类型
        Route::get('/types', [AssessmentTeacherController::class, 'types'])->name('assessment.all.list');
        // 教务端 测评库列表
        Route::get('/list', [AssessmentTeacherController::class, 'assessmentList'])->name('assessment.schoolhas.list');
        // 教师端提交测评答案并直接生成报告（不保存数据）
        Route::post('/submit_and_report', [AssessmentProcessController::class, 'teacherSubmitAndReport'])->name('assessment.teacher.submit_and_report');

        // 教务端 计划管理
        Route::prefix('schedule')->group(function () {
            Route::get('/', [ScheduleController::class, 'index'])->name('assessment.schedule.index');
            Route::get('/{id}', [ScheduleController::class, 'show'])->name('assessment.schedule.show')->where('id', '[0-9]+');
            Route::post('/', [ScheduleController::class, 'store'])->name('assessment.schedule.store');
            Route::put('/{schedule_id}', [ScheduleController::class, 'update'])->name('assessment.schedule.update')->where('schedule_id', '[0-9]+');
            Route::delete('/{schedule_id}', [ScheduleController::class, 'destroy'])->name('assessment.schedule.destroy')->where('schedule_id', '[0-9]+');

            // 教务端 计划及计划下任务的统计详情(计划信息、状态、完成人数、完成率、平均完成时间)
            Route::get('/stat_detail', [ScheduleController::class, 'statDetail'])->name('assessment.schedule.statDetail');
            // 教务端 计划完成名单情况
            Route::get('/student_list', [ScheduleController::class, 'detail'])->name('assessment.schedule.detail');
            // 教务端 计划中某个测评的得分情况
            Route::get('/students_score', [ScheduleController::class, 'getStudentsScore'])->name('assessment.schedule.students_score');
            // 教务端 计划中某个测评的个人报告批量下载
            Route::get('/report_download', [PdfZipController::class, 'batchDownloadReportPdf'])->name('assessment.schedule.detail');
            // 教务端 计划中某个测评的题目统计
            Route::get('/question_stat', [ScheduleController::class, 'questionStat'])->name('assessment.schedule.question_stat');
            // 教务端 计划中某个测评的题目统计导出
            Route::get('/question_export', [ScheduleController::class, 'questionExport'])->name('assessment.schedule.questionExport');
        });

        // 教务端 计划未完成学生名单
        Route::get('/{id}/schedule_classes', [ScheduleController::class, 'scheduleClasses'])->name('assessment.schedule.incompleteStudents')->where('id', '[0-9]+');

        // 教务端 计划未完成学生名单
        Route::get('/{id}/incomplete_students', [ScheduleController::class, 'incompleteStudents'])->name('assessment.schedule.incompleteStudents')->where('id', '[0-9]+');

        // 教务端 综合报告列表
        Route::get('composite_report/list', [AssessmentTeacherController::class, 'compositeReportList'])->name('assessment.report.show');
        // 教务端 综合报告批量下载
        Route::get('composite_report/download', [AssessmentTeacherController::class, 'batchDownloadReportPdf'])->name('assessment.report.show');

    });

    // 学生端 测评相关接口
    Route::prefix('student_platform')->group(function () {
        // 学生端 自主探索列表
        Route::get('/autonomous_exploration', [AssessmentStudentController::class, 'assessmentList'])->name('assessment.schedule.assessmentList');
        // 学生端 我的测评计划列表
        Route::get('/my_schedules', [AssessmentStudentController::class, 'mySchedules'])->name('assessment.schedule.mySchedules');
        // 学生端 获取计划名称
        Route::get('/schedule_name/{id}', [AssessmentStudentController::class,'scheduleName'])->name('assessment.schedule.scheduleName');
        // 学生端 我的测评计划列表
        Route::get('/my_tasks/{id}', [AssessmentStudentController::class, 'myTasks'])->name('assessment.schedule.myTasks');
        //个人综合报告
        Route::get('composite_report', [AssessmentStudentController::class, 'compositeReport'])->name('assessment.compositeReport.show');
        //我的生涯画像--标签
        Route::get('/portray/tag', [AssessmentStudentController::class, 'tag'])->name('assessment.portray.tag');
        //我的生涯画像-生涯关键词云
        Route::get('/portray/keyword', [AssessmentStudentController::class, 'keyword'])->name('assessment.compositeReport.keyword');
        //我的生涯画像-生涯数据
        Route::get('portray/assessment_data', [AssessmentStudentController::class, 'assessmentData'])->name('assessment.portray.assessmentData');
        // 个体报告没有pdf链接时调用此api生成pdf
        Route::get('generate_pdf', [PdfZipController::class, 'generatePdf'])->name('assessment.report.generatePdf');
        // 综合报告没有pdf链接时调用此api生成pdf
        Route::get('generate_composite_pdf', [PdfZipController::class, 'generateCompositePdf'])->name('assessment.report.generatePdf');
    });

    // 测评标准过程接口（不要掺杂其他的接口）
    Route::prefix('process')->group(function () {
        // 学生端 开始测评首页
        Route::get('/assessment_info/{id}', [AssessmentStudentController::class, 'index'])->name('assessment.start.assessment_info');
        //问题列表 根据测评ID和类型获取问题列表
        Route::get('questions', [QuestionController::class, 'questionList'])->name('assessment.question.list');
        //保存答案
        Route::post('answers', [AssessmentProcessController::class, 'saveAnswer'])->name('assessment.answers.save');
        //个人报告
        Route::get('report', [AssessmentProcessController::class, 'report'])->name('assessment.report.show');
        
        //团体报告
        Route::get('group_report', [AssessmentProcessController::class, 'groupReport'])->name('assessment.groupReport.show');
    });

    // 获取我的所有测评报告
    Route::get('my_reports', [AssessmentStudentController::class, 'getUserReports'])->name('assessment.myReports.show');
});

//不需要鉴权的接口
Route::group(['prefix' => 'v1/assessment'], function () {
    //个人报告,生成pdf的html页面需要调用此接口
    Route::get('report_to_pdf', [AssessmentProcessController::class, 'report'])->name('assessment.report.reportToPdf');
    //个人综合报告
    Route::get('composite_report_to_pdf', [AssessmentStudentController::class, 'compositeReport'])->name('assessment.compositeReport.show');
});
