<?php

namespace App\Services\Admin;

use App\Constants\RedisKeyConstants;
use App\Exceptions\BusinessException;
use App\Models\Admin\Menu;
use App\Models\Admin\Organization;
use App\Models\Admin\OrganizationHasMenu;
use App\Services\BaseService;
use App\Services\Tool\RedisUtilService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\DB;
use App\Repositories\MenuRepository;

class MenuService extends BaseService
{
    // 构造函数：注入RedisUtils
    public function __construct(protected RedisUtilService $redisUtils, protected MenuRepository $menuRepository)
    {
    }

    /*
     * 获取菜单树形结构
     * $result: 菜单数据集合
     * $type: 1 需要根据菜单有效期时间过滤菜单 2，不需要
     */
    public function getTreeMenus($result, $type=2)
    {
        $menu_list = [];
        // 递归获取子菜单
        $menu_map = $result->keyBy('menu_id');
        foreach ($menu_map as $item) {
            if ($item->parent_id == 0) {
                if($type===1){
                    // 如果菜单没有设置有效期，则不返回该菜单
                    if(!$item->date_start || !$item->date_due){
                        continue;
                    }

                    $date_start = Carbon::parse($item->date_start)->timestamp;
                    $date_due = Carbon::parse($item->date_due)->timestamp;
                    // 如果当前菜单不在有效时间范围内，则不返回该菜单
                    if($date_start > time() || $date_due < time()){
                        continue;
                    }
                }
                $item->children = $this->getChildrenMenus($item->menu_id, $menu_map);
                $menu_list[] = $item;
            }
        }

        return $menu_list;
    }

    // 递归获取子菜单
    private function getChildrenMenus($parentId, $menu_map) {
        $children = [];
        foreach ($menu_map as $item) {
            if ($item->parent_id == $parentId) {
                $item->children = $this->getChildrenMenus($item->menu_id, $menu_map);
                $children[] = $item;
            }
        }
        return $children;
    }


    public function getChildrenMenusByParentId($request)
    {
        return Menu::where('parent_id', $request['parent_id'])
            ->where('type', $request['type'])
            ->where('status', 1)
            ->orderBy('sort', 'asc')
            ->get();
    }

    
    public function store($request)
    {
        try {
            DB::beginTransaction();
            $parent_id = $request->parent_id;
            $type = $request->type;
            if ($parent_id != 0) {
                // 查找父级菜单
                $parentMenu = Menu::find($parent_id);
                if (!$parentMenu) {
                    DB::rollBack();
                    $this->throwBusinessException("父级菜单不存在");
                }
                // 取得父级菜单类型
                $type = $parentMenu->type;
                $crowd = $request->crowd ?? null;
                
                // 处理crowd属性，确保子菜单的crowd是父菜单crowd的子集
                if ($crowd && $type == 3) {
                    $this->updateParentMenuCrowd($parentMenu, $crowd, $request->user()->real_name);
                }
            }
            $sortCount = Menu::where('parent_id', $parent_id)
                ->where('type', $type)->count();

            $data = filterRequestData('menus');
            $data['creator'] = $request->user()->real_name;
            $data['updater'] = $request->user()->real_name;
            $data['type'] = $type;
            $data['sort'] = $sortCount + 1;
            
            $record = Menu::forceCreate($data);

            DB::commit();

            // 删除所有角色菜单缓存
            $this->clearMenuCache($record->type);
        } catch (\Exception $e) {
            DB::rollBack();
            throw new BusinessException("新增菜单逻辑错误", 500, $e->getMessage());
        }
    }

    public function update($request, $id)
    {
        try {
            DB::beginTransaction();
            $record = Menu::find($id);
            if (!$record) {
                throw new BusinessException('菜单不存在');
            }
            $data = filterRequestData('menus');
            $data['updater'] = $request->user()->real_name;
            $record->fill($data)->save();

            $parent_id = $request->parent_id;
            if ($parent_id != 0 && $record->type == 3) {
                // 查找父级菜单
                $parentMenu = Menu::find($parent_id);
                if (!$parentMenu) {
                    return $this->notFound('父级菜单不存在');
                }
                $crowd = $request->crowd ?? null;

                // 处理crowd属性，确保子菜单的crowd是父菜单crowd的子集
                if ($crowd) {
                    $this->updateParentMenuCrowd($parentMenu, $crowd, $request->user()->real_name);
                }
            }

            DB::commit();

            // 删除所有角色菜单缓存
            $this->clearMenuCache($record->type);
        } catch (\Exception $e) {
            DB::rollBack();
            throw new BusinessException("更新菜单逻辑错误", 500, $e->getMessage());
        }
    }

    /**
     * 递归更新父级菜单的crowd属性
     * @param Menu $menu 当前菜单
     * @param array $crowd 需要添加的crowd值
     * @param string $updater 更新者
     */
    private function updateParentMenuCrowd($menu, $crowd, $updater)
    {
        try {
            $parentCrowd = $menu->crowd ?? '';
            
            // 如果父菜单没有crowd属性或者子菜单的crowd不是父菜单crowd的子集
            if (empty($parentCrowd)) {
                // 更新父菜单的crowd属性
                $menu->crowd = $crowd;
                $menu->updater = $updater;
                $menu->save();
                
                // 递归处理上级菜单
                if ($menu->parent_id != 0) {
                    $grandParentMenu = Menu::find($menu->parent_id);
                    if ($grandParentMenu) {
                        $this->updateParentMenuCrowd($grandParentMenu, $crowd, $updater);
                    }
                }
            } else {
                $parentCrowdArray = $parentCrowd;
                $needUpdate = false;
                
                // 检查子菜单的每个crowd值是否在父菜单中
                foreach ($crowd as $value) {
                    if (!in_array($value, $parentCrowdArray)) {
                        $parentCrowdArray[] = $value;
                        $needUpdate = true;
                    }
                }
                
                // 如果需要更新父菜单
                if ($needUpdate) {
                    $menu->crowd = $parentCrowdArray;
                    $menu->updater = $updater;
                    $menu->save();
                    
                    // 递归处理上级菜单
                    if ($menu->parent_id != 0) {
                        $grandParentMenu = Menu::find($menu->parent_id);
                        if ($grandParentMenu) {
                            $this->updateParentMenuCrowd($grandParentMenu, $menu->crowd, $updater);
                        }
                    }
                }
            }

            $this->clearMenuCache($menu->type);
        } catch (\Exception $e) {
            throw new BusinessException("更新父级菜单逻辑错误", 500, $e->getMessage());
        }
    }

    public function setMenuSort($request)
    {
        try {
            DB::beginTransaction();
            $up_menu_id = $request['up_menu_id'];
            $down_menu_id = $request['down_menu_id'];
            $up_menu = Menu::find($up_menu_id);
            $down_menu = Menu::find($down_menu_id);

            if (!$up_menu || !$down_menu) {
                throw new BusinessException("菜单不存在");
            }
            if ($up_menu->parent_id != $down_menu->parent_id) {
                throw new BusinessException("只能调整同级菜单的顺序");
            }
            if ($up_menu->sort < $down_menu->sort) {
                throw new BusinessException("请调整正确的顺序");
            }

            $up_sort = $up_menu->sort;
            $down_sort = $down_menu->sort;
            $up_menu->sort = $down_sort;
            $down_menu->sort = $up_sort;
            $up_menu->save();
            $down_menu->save();
            DB::commit();

            $this->clearMenuCache($up_menu->type);
        } catch (\Exception $e) {
            DB::rollBack();
            throw new BusinessException("设置菜单顺序逻辑错误", 500, $e->getMessage());
        }
    }

    public function clearMenuCache(int $type): void
    {
        // // 清理当前菜单列表缓存
        // $key = keyBuilder(RedisKeyConstants::ADMIN['BASE_MENUS'], $type);
        // Redis::del($key);

        // 清理角色和组织的菜单缓存
        $this->redisUtils->deleteKeysByPrefixOptimized(RedisKeyConstants::SYSTEM['ROLE_MENUS']);
        $this->redisUtils->deleteKeysByPrefixOptimized(RedisKeyConstants::SYSTEM['ORG_MENUS']);
    }

    /**
     * 基础菜单同步或取消同步给机构购买菜单，如果
     * 1，如果是同步：机构拥有当前菜单父级菜单，则同步给机构当前菜单
     * 2，如果是取消同步：机构购买当前菜单，则取消机构当前菜单
     *
     * $menu_id：菜单ID
     * $sync_type：同步类型 1：同步 2：取消同步
     */
    public function grantMenuToAllOrgs($request)
    {
        try {
            $menu_id = $request['menu_id'];
            $sync_type = $request['sync_type'];

            $menu = Menu::find($menu_id);
            if (!$menu) {
                return;
            }

            DB::beginTransaction();

            // 同步当前菜单，同步给拥有当前菜单父级菜单所有机构
            if ($sync_type == 1) {
                // 如果是顶级菜单，不需要查找机构是否拥有父级菜单
                if($menu->parent_id != 0){
                    // 获取所有购买了父模块的机构ID
                    $orgIds = OrganizationHasMenu::where('menu_id', $menu->parent_id)
                        ->pluck('organization_id')
                        ->toArray();

                    if (empty($orgIds)) {
                        return;
                    }

                    // 获取所有已有当前菜单的机构ID
                    $existingPermissions = OrganizationHasMenu::where('menu_id', $menu->id)
                        ->whereIn('organization_id', $orgIds)
                        ->pluck('organization_id')
                        ->toArray();

                    foreach ($orgIds as $orgId) {
                        // 批量插入，避免重复
                        if (!in_array($orgId, $existingPermissions)) {
                            $newPermissions = [
                                'organization_id' => $orgId,
                                'menu_id' => $menu->id,
                                'parent_id' => $menu->parent_id,
                                'sort' => $menu->sort,
                                'status' => 1,
                                'creator' => $menu->creator,
                                'updater' => $menu->creator,
                            ];

                            OrganizationHasMenu::create($newPermissions);
                        }
                    }
                } else {
                    // 查找所有学校机构
                    $schoolOrgIds = Organization::where('model_type', 'school')
                        ->pluck('id')
                        ->toArray();

                    // 获取所有已有当前菜单的机构ID
                    $orgIds = OrganizationHasMenu::where('menu_id', $menu->id)
                        ->pluck('organization_id')
                        ->toArray();

                    // 排除学校机构已有当前菜单的机构ID
                    $orgIds = array_diff($orgIds, $schoolOrgIds);
                    foreach ($orgIds as $orgId) {
                        $newPermissions = [
                            'organization_id' => $orgId,
                            'menu_id' => $menu->id,
                            'parent_id' => 0,
                            'sort' => $menu->sort,
                            'status' => 1,
                            'creator' => $request->user()->real_name,
                            'updater' => $request->user()->real_name,
                        ];

                        OrganizationHasMenu::create($newPermissions);
                    }

                }
            } else if ($sync_type == 2) {
                // 获取所有购买了当前菜单的机构ID
                $orgIds = OrganizationHasMenu::where('menu_id', $menu->id)
                    ->pluck('id')
                    ->toArray();

                if (empty($orgIds)) {
                    return;
                }

                // 反向取消
                OrganizationHasMenu::where('menu_id', $menu->id)
                    ->whereIn('organization_id', $orgIds)
                    ->delete();
            }

            DB::commit();

            // 删除所有角色菜单缓存
            $this->clearMenuCache($menu->type);
        } catch (\Exception $e) {
            throw new BusinessException("给机构同步或取消同步基础菜单逻辑错误", 500, $e->getMessage());
        }
    }


    /**
     * crowdMenus
     */
    public function crowdMenus($crowd, $organization_id)
    {
        // 查询适用于指定人群的菜单ID
        $menuIds = $this->menuRepository->getLeafMenus($crowd)->pluck('id')->toArray();

        if (empty($menuIds)) {
            return [];
        }
        
        // 查询机构拥有的菜单中，属于这些菜单的ID列表
        $orgMenuIds = $this->menuRepository->orgMenus($organization_id, $menuIds);
        return $orgMenuIds;
    }

}
